"""
Main window for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Optional, Dict, Any
import threading

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import show_error, show_info, confirm_action
from analysis.ollama_client import OllamaClient
from gui.topic_input import TopicInputPanel
from gui.analysis_panel import AnalysisPanel
from gui.results_viewer import ResultsViewer

class MainWindow(LoggerMixin):
    """Main application window"""
    
    def __init__(self, root: tk.Tk, config: Config):
        self.root = root
        self.config = config
        self.ollama_client = OllamaClient(config)
        
        # Initialize components
        self.topic_input = None
        self.analysis_panel = None
        self.results_viewer = None
        
        # State variables
        self.current_analysis = None
        self.analysis_running = False
        
        self.setup_ui()
        self.setup_menu()
        self.check_ollama_connection()
    
    def setup_ui(self):
        """Set up the main user interface"""
        # Configure root window
        self.root.configure(bg='#f0f0f0')
        
        # Create main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create paned window for resizable panels
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Topic Input
        left_frame = ttk.LabelFrame(paned_window, text="Topic Input", padding=10)
        self.topic_input = TopicInputPanel(left_frame, self.config)
        paned_window.add(left_frame, weight=1)
        
        # Right panel container
        right_container = ttk.Frame(paned_window)
        paned_window.add(right_container, weight=2)
        
        # Right panel - Analysis and Results (vertical split)
        right_paned = ttk.PanedWindow(right_container, orient=tk.VERTICAL)
        right_paned.pack(fill=tk.BOTH, expand=True)
        
        # Analysis panel
        analysis_frame = ttk.LabelFrame(right_paned, text="Analysis Control", padding=10)
        self.analysis_panel = AnalysisPanel(analysis_frame, self.config, self.on_start_analysis)
        right_paned.add(analysis_frame, weight=1)
        
        # Results panel
        results_frame = ttk.LabelFrame(right_paned, text="Results", padding=10)
        self.results_viewer = ResultsViewer(results_frame, self.config)
        right_paned.add(results_frame, weight=2)
        
        # Status bar
        self.setup_status_bar()
    
    def setup_status_bar(self):
        """Set up the status bar"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # Status label
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.status_frame, 
            variable=self.progress_var,
            mode='determinate',
            length=200
        )
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # Connection status
        self.connection_var = tk.StringVar(value="Checking...")
        self.connection_label = ttk.Label(
            self.status_frame, 
            textvariable=self.connection_var,
            foreground="orange"
        )
        self.connection_label.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def setup_menu(self):
        """Set up the menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Analysis", command=self.new_analysis)
        file_menu.add_command(label="Open Analysis", command=self.open_analysis)
        file_menu.add_command(label="Save Analysis", command=self.save_analysis)
        file_menu.add_separator()
        file_menu.add_command(label="Export Results", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Clear All", command=self.clear_all)
        edit_menu.add_command(label="Settings", command=self.show_settings)
        
        # Analysis menu
        analysis_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Analysis", menu=analysis_menu)
        analysis_menu.add_command(label="Start Analysis", command=self.start_analysis)
        analysis_menu.add_command(label="Stop Analysis", command=self.stop_analysis)
        analysis_menu.add_separator()
        analysis_menu.add_command(label="View Connections", command=self.view_connections)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="User Guide", command=self.show_user_guide)
    
    def check_ollama_connection(self):
        """Check Ollama connection in background"""
        def check_connection():
            try:
                if self.ollama_client.is_available():
                    self.root.after(0, lambda: self.update_connection_status("Connected", "green"))
                    models = self.ollama_client.list_models()
                    self.root.after(0, lambda: self.analysis_panel.update_models(models))
                else:
                    self.root.after(0, lambda: self.update_connection_status("Disconnected", "red"))
            except Exception as e:
                self.logger.error(f"Connection check failed: {e}")
                self.root.after(0, lambda: self.update_connection_status("Error", "red"))
        
        threading.Thread(target=check_connection, daemon=True).start()
    
    def update_connection_status(self, status: str, color: str):
        """Update connection status display"""
        self.connection_var.set(f"Ollama: {status}")
        self.connection_label.configure(foreground=color)
    
    def update_status(self, message: str):
        """Update status bar message"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def update_progress(self, value: float):
        """Update progress bar"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def on_start_analysis(self, analysis_config: Dict[str, Any]):
        """Handle analysis start request"""
        if self.analysis_running:
            show_error("Analysis Running", "An analysis is already in progress.")
            return
        
        # Get topic data
        topic_data = self.topic_input.get_topic_data()
        if not topic_data:
            show_error("No Topic", "Please enter a topic to analyze.")
            return
        
        # Start analysis in background thread
        self.start_analysis_thread(topic_data, analysis_config)
    
    def start_analysis_thread(self, topic_data: Dict[str, Any], analysis_config: Dict[str, Any]):
        """Start analysis in background thread"""
        def run_analysis():
            try:
                self.analysis_running = True
                self.root.after(0, lambda: self.update_status("Starting analysis..."))
                self.root.after(0, lambda: self.update_progress(10))
                
                # Perform analysis based on type
                analysis_type = analysis_config.get("type", "iterative")
                
                if analysis_type == "iterative":
                    results = self.run_iterative_analysis(topic_data, analysis_config)
                elif analysis_type == "recursive":
                    results = self.run_recursive_analysis(topic_data, analysis_config)
                else:
                    results = {"error": "Unknown analysis type"}
                
                # Update UI with results
                self.root.after(0, lambda: self.display_results(results))
                self.root.after(0, lambda: self.update_status("Analysis completed"))
                self.root.after(0, lambda: self.update_progress(100))
                
            except Exception as e:
                self.logger.error(f"Analysis failed: {e}")
                self.root.after(0, lambda: show_error("Analysis Error", f"Analysis failed: {str(e)}"))
                self.root.after(0, lambda: self.update_status("Analysis failed"))
            finally:
                self.analysis_running = False
                self.root.after(0, lambda: self.update_progress(0))
        
        threading.Thread(target=run_analysis, daemon=True).start()
    
    def run_iterative_analysis(self, topic_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Run iterative analysis"""
        results = {
            "type": "iterative",
            "topic": topic_data.get("title", ""),
            "sub_analyses": [],
            "connections": [],
            "metadata": {
                "model": config.get("model", self.ollama_client.default_model),
                "timestamp": self.get_timestamp()
            }
        }

        main_topic = topic_data.get("title", "")
        sub_topics = topic_data.get("sub_topics", [])

        # Analyze main topic
        self.root.after(0, lambda: self.update_status("Analyzing main topic..."))
        main_analysis = self.ollama_client.analyze_topic(main_topic, analysis_type="detailed")
        if main_analysis:
            results["main_analysis"] = main_analysis

        # Analyze sub-topics iteratively
        total_subtopics = len(sub_topics)
        for i, sub_topic in enumerate(sub_topics):
            if not self.analysis_running:
                break

            progress = 20 + (60 * (i + 1) / total_subtopics)
            self.root.after(0, lambda p=progress: self.update_progress(p))
            self.root.after(0, lambda st=sub_topic: self.update_status(f"Analyzing: {st}"))

            analysis = self.ollama_client.analyze_topic(sub_topic, context=main_topic)
            if analysis:
                results["sub_analyses"].append({
                    "topic": sub_topic,
                    "analysis": analysis
                })

        # Find connections
        if len(sub_topics) > 1:
            self.root.after(0, lambda: self.update_status("Finding connections..."))
            self.root.after(0, lambda: self.update_progress(90))

            connections = self.ollama_client.find_connections([main_topic] + sub_topics)
            if connections:
                results["connections"] = connections

        return results

    def run_recursive_analysis(self, topic_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Run recursive analysis"""
        results = {
            "type": "recursive",
            "topic": topic_data.get("title", ""),
            "analysis_tree": {},
            "metadata": {
                "model": config.get("model", self.ollama_client.default_model),
                "max_depth": config.get("max_depth", 3),
                "timestamp": self.get_timestamp()
            }
        }

        main_topic = topic_data.get("title", "")
        max_depth = config.get("max_depth", 3)

        # Start recursive analysis
        results["analysis_tree"] = self.analyze_recursive(main_topic, 0, max_depth)

        return results

    def analyze_recursive(self, topic: str, current_depth: int, max_depth: int) -> Dict[str, Any]:
        """Recursively analyze a topic"""
        if current_depth >= max_depth or not self.analysis_running:
            return {"topic": topic, "analysis": None, "subtopics": []}

        progress = 20 + (70 * current_depth / max_depth)
        self.root.after(0, lambda p=progress: self.update_progress(p))
        self.root.after(0, lambda t=topic: self.update_status(f"Analyzing (depth {current_depth}): {t}"))

        # Analyze current topic
        analysis = self.ollama_client.analyze_topic(topic, analysis_type="detailed")

        # Generate subtopics for next level
        subtopic_prompt = f"Based on the topic '{topic}', suggest 3-5 related subtopics for deeper analysis. Return only the subtopic names, one per line."
        subtopics_text = self.ollama_client.generate(subtopic_prompt)

        subtopics = []
        if subtopics_text:
            subtopic_lines = [line.strip() for line in subtopics_text.split('\n') if line.strip()]
            subtopics = subtopic_lines[:5]  # Limit to 5 subtopics

        # Recursively analyze subtopics
        subtopic_analyses = []
        for subtopic in subtopics:
            if self.analysis_running:
                subtopic_analysis = self.analyze_recursive(subtopic, current_depth + 1, max_depth)
                subtopic_analyses.append(subtopic_analysis)

        return {
            "topic": topic,
            "analysis": analysis,
            "subtopics": subtopic_analyses,
            "depth": current_depth
        }

    def get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def display_results(self, results: Dict[str, Any]):
        """Display analysis results"""
        self.results_viewer.display_results(results)
    
    # Menu command implementations
    def new_analysis(self):
        """Create new analysis"""
        if self.analysis_running:
            show_error("Analysis Running", "Cannot create new analysis while one is running.")
            return
        
        if confirm_action("New Analysis", "Clear current analysis and start new?"):
            self.clear_all()
    
    def open_analysis(self):
        """Open saved analysis"""
        filename = filedialog.askopenfilename(
            title="Open Analysis",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            # Implementation for loading analysis
            show_info("Open Analysis", f"Opening: {filename}")
    
    def save_analysis(self):
        """Save current analysis"""
        filename = filedialog.asksaveasfilename(
            title="Save Analysis",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            # Implementation for saving analysis
            show_info("Save Analysis", f"Saving: {filename}")
    
    def export_results(self):
        """Export analysis results"""
        if not self.results_viewer.has_results():
            show_error("No Results", "No analysis results to export.")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Export Results",
            defaultextension=".md",
            filetypes=[("Markdown files", "*.md"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.results_viewer.export_results(filename)
    
    def clear_all(self):
        """Clear all data"""
        self.topic_input.clear()
        self.analysis_panel.reset()
        self.results_viewer.clear()
        self.update_status("Ready")
    
    def show_settings(self):
        """Show settings dialog"""
        show_info("Settings", "Settings dialog will be implemented")
    
    def start_analysis(self):
        """Start analysis from menu"""
        self.analysis_panel.start_analysis()
    
    def stop_analysis(self):
        """Stop current analysis"""
        if self.analysis_running:
            self.analysis_running = False
            self.update_status("Analysis stopped")
    
    def view_connections(self):
        """View connection analysis"""
        show_info("Connections", "Connection viewer will be implemented")
    
    def show_about(self):
        """Show about dialog"""
        about_text = """AI Analysis Program v1.0
        
A comprehensive tool for topical AI analysis using Ollama API.

Features:
- Iterative and recursive analysis
- Connection finding
- Interactive results visualization
- Markdown export

Built with Python and Tkinter."""
        
        messagebox.showinfo("About", about_text)
    
    def show_user_guide(self):
        """Show user guide"""
        show_info("User Guide", "User guide will be implemented")
    
    def confirm_close(self) -> bool:
        """Confirm application closing"""
        if self.analysis_running:
            return confirm_action("Exit", "Analysis is running. Exit anyway?")
        return True
    
    def cleanup(self):
        """Cleanup resources"""
        self.analysis_running = False
        if self.topic_input:
            self.topic_input.cleanup()
        if self.analysis_panel:
            self.analysis_panel.cleanup()
        if self.results_viewer:
            self.results_viewer.cleanup()
    
    def on_closing(self):
        """Handle window closing"""
        if self.confirm_close():
            self.root.quit()
