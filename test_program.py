#!/usr/bin/env python3
"""
Test script for AI Analysis Program
Tests basic functionality without requiring Ollama
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from utils.config import Config
        print("✓ Config imported successfully")
        
        from utils.logging_setup import setup_logging
        print("✓ Logging setup imported successfully")
        
        from utils.helpers import sanitize_filename, generate_hash
        print("✓ Helpers imported successfully")
        
        from analysis.ollama_client import OllamaClient
        print("✓ Ollama client imported successfully")
        
        from analysis.mindmap_generator import MindMapGenerator
        print("✓ Mind map generator imported successfully")
        
        from gui.main_window import MainWindow
        print("✓ Main window imported successfully")
        
        from gui.topic_input import TopicInputPanel
        print("✓ Topic input panel imported successfully")
        
        from gui.analysis_panel import AnalysisPanel
        print("✓ Analysis panel imported successfully")
        
        from gui.results_viewer import ResultsViewer
        print("✓ Results viewer imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_config():
    """Test configuration system"""
    print("\nTesting configuration...")
    
    try:
        from utils.config import Config
        
        config = Config()
        
        # Test default values
        assert config.get("ollama.base_url") == "http://localhost:11434"
        assert config.get("analysis.max_recursive_depth") == 5
        assert config.get("export.default_format") == "markdown"
        
        # Test setting values
        config.set("test.value", "test_data")
        assert config.get("test.value") == "test_data"
        
        print("✓ Configuration system working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_mindmap_generator():
    """Test mind map generator"""
    print("\nTesting mind map generator...")
    
    try:
        from utils.config import Config
        from analysis.mindmap_generator import MindMapGenerator
        
        config = Config()
        generator = MindMapGenerator(config)
        
        # Test data conversion
        sample_results = {
            "type": "iterative",
            "topic": "Test Topic",
            "main_analysis": "This is a test analysis of the main topic.",
            "sub_analyses": [
                {
                    "topic": "Sub-topic 1",
                    "analysis": "Analysis of sub-topic 1"
                },
                {
                    "topic": "Sub-topic 2", 
                    "analysis": "Analysis of sub-topic 2"
                }
            ],
            "connections": "These topics are connected through common themes.",
            "metadata": {
                "model": "test_model",
                "timestamp": "2024-01-01T12:00:00"
            }
        }
        
        # Convert to mind map data
        mindmap_data = generator.convert_analysis_to_mindmap(sample_results)
        
        # Verify structure
        assert "nodes" in mindmap_data
        assert "links" in mindmap_data
        assert "metadata" in mindmap_data
        assert len(mindmap_data["nodes"]) >= 3  # Root + 2 sub-topics + connections
        
        print("✓ Mind map generator working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Mind map generator test failed: {e}")
        return False

def test_file_structure():
    """Test that required directories and files exist"""
    print("\nTesting file structure...")
    
    required_dirs = [
        "utils",
        "gui", 
        "analysis",
        "data"
    ]
    
    required_files = [
        "main.py",
        "requirements.txt",
        "utils/__init__.py",
        "utils/config.py",
        "utils/logging_setup.py",
        "utils/helpers.py",
        "gui/__init__.py",
        "gui/main_window.py",
        "gui/topic_input.py",
        "gui/analysis_panel.py",
        "gui/results_viewer.py",
        "analysis/__init__.py",
        "analysis/ollama_client.py",
        "analysis/mindmap_generator.py",
        "data/__init__.py"
    ]
    
    all_good = True
    
    # Check directories
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✓ Directory {dir_name} exists")
        else:
            print(f"✗ Directory {dir_name} missing")
            all_good = False
    
    # Check files
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"✓ File {file_name} exists")
        else:
            print(f"✗ File {file_name} missing")
            all_good = False
    
    return all_good

def create_sample_analysis():
    """Create a sample analysis for testing"""
    return {
        "type": "iterative",
        "topic": "Artificial Intelligence in Healthcare",
        "main_analysis": """
        Artificial Intelligence in Healthcare represents a transformative force that is reshaping 
        medical practice, diagnosis, and patient care. This technology encompasses machine learning 
        algorithms, natural language processing, computer vision, and robotics to enhance medical 
        decision-making, improve patient outcomes, and streamline healthcare operations.
        
        Key areas of impact include diagnostic imaging, drug discovery, personalized medicine, 
        and administrative efficiency. AI systems can analyze medical images with remarkable 
        accuracy, sometimes surpassing human radiologists in detecting certain conditions.
        """,
        "sub_analyses": [
            {
                "topic": "Diagnostic Imaging",
                "analysis": """
                AI in diagnostic imaging has shown remarkable progress, particularly in radiology 
                and pathology. Deep learning models can detect cancers, fractures, and other 
                abnormalities in X-rays, CT scans, and MRIs with high accuracy. Companies like 
                Google's DeepMind have developed systems that can diagnose over 50 eye diseases 
                from retinal scans.
                """
            },
            {
                "topic": "Drug Discovery",
                "analysis": """
                AI is accelerating drug discovery by predicting molecular behavior, identifying 
                potential drug targets, and optimizing clinical trials. Machine learning models 
                can analyze vast databases of chemical compounds and biological data to identify 
                promising drug candidates, potentially reducing development time from decades to years.
                """
            },
            {
                "topic": "Personalized Medicine",
                "analysis": """
                AI enables personalized treatment plans by analyzing patient genetics, medical 
                history, and lifestyle factors. This approach allows for tailored therapies that 
                are more effective and have fewer side effects. Precision oncology is a leading 
                example where AI helps match cancer patients with the most suitable treatments.
                """
            },
            {
                "topic": "Administrative Efficiency",
                "analysis": """
                AI streamlines healthcare administration through automated scheduling, billing, 
                and documentation. Natural language processing can extract information from 
                clinical notes, while chatbots handle routine patient inquiries. This reduces 
                administrative burden on healthcare providers and improves operational efficiency.
                """
            }
        ],
        "connections": """
        The sub-topics in AI healthcare are deeply interconnected. Diagnostic imaging provides 
        data that feeds into personalized medicine algorithms. Drug discovery benefits from 
        diagnostic insights and personalized patient profiles. Administrative efficiency 
        supports all other areas by ensuring smooth operations and data management.
        
        Common themes include data integration, pattern recognition, and the need for regulatory 
        compliance. All areas face similar challenges around data privacy, algorithm bias, 
        and the need for clinical validation.
        """,
        "metadata": {
            "model": "test_model",
            "timestamp": "2024-01-01T12:00:00",
            "analysis_duration": "5 minutes"
        }
    }

def test_mindmap_generation():
    """Test complete mind map generation"""
    print("\nTesting complete mind map generation...")
    
    try:
        from utils.config import Config
        from analysis.mindmap_generator import MindMapGenerator
        
        config = Config()
        generator = MindMapGenerator(config)
        
        # Create sample analysis
        sample_analysis = create_sample_analysis()
        
        # Generate mind map
        output_path = generator.generate_interactive_mindmap(
            sample_analysis, 
            "test_mindmap.html"
        )
        
        # Verify file was created
        if Path(output_path).exists():
            print(f"✓ Mind map generated successfully: {output_path}")
            
            # Check file size (should be substantial)
            file_size = Path(output_path).stat().st_size
            if file_size > 10000:  # At least 10KB
                print(f"✓ Mind map file has reasonable size: {file_size} bytes")
                return True
            else:
                print(f"✗ Mind map file too small: {file_size} bytes")
                return False
        else:
            print("✗ Mind map file was not created")
            return False
            
    except Exception as e:
        print(f"✗ Mind map generation test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("AI Analysis Program - Test Suite")
    print("=" * 40)
    
    tests = [
        test_file_structure,
        test_imports,
        test_config,
        test_mindmap_generator,
        test_mindmap_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The program is ready to use.")
        print("\nTo run the program:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Make sure Ollama is running (for AI functionality)")
        print("3. Run: python main.py")
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
