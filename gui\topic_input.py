"""
Topic input panel for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, List, Optional
import json

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import validate_topic_structure, show_error, show_info

class TopicInputPanel(LoggerMixin):
    """Panel for topic input and management"""
    
    def __init__(self, parent: tk.Widget, config: Config):
        self.parent = parent
        self.config = config
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the topic input interface"""
        # Main topic section
        main_topic_frame = ttk.LabelFrame(self.parent, text="Main Topic", padding=5)
        main_topic_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Topic title
        ttk.Label(main_topic_frame, text="Title:").pack(anchor=tk.W)
        self.topic_title_var = tk.StringVar()
        self.topic_title_entry = ttk.Entry(
            main_topic_frame, 
            textvariable=self.topic_title_var,
            font=('Arial', 10)
        )
        self.topic_title_entry.pack(fill=tk.X, pady=(2, 5))
        
        # Topic description
        ttk.Label(main_topic_frame, text="Description:").pack(anchor=tk.W)
        self.topic_desc_text = tk.Text(
            main_topic_frame, 
            height=4, 
            wrap=tk.WORD,
            font=('Arial', 9)
        )
        self.topic_desc_text.pack(fill=tk.X, pady=(2, 0))
        
        # Sub-topics section
        subtopics_frame = ttk.LabelFrame(self.parent, text="Sub-topics", padding=5)
        subtopics_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Sub-topics toolbar
        toolbar_frame = ttk.Frame(subtopics_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(
            toolbar_frame, 
            text="Add Sub-topic", 
            command=self.add_subtopic
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            toolbar_frame, 
            text="Remove Selected", 
            command=self.remove_subtopic
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            toolbar_frame, 
            text="Clear All", 
            command=self.clear_subtopics
        ).pack(side=tk.LEFT)
        
        # Sub-topics list
        list_frame = ttk.Frame(subtopics_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Listbox with scrollbar
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.subtopics_listbox = tk.Listbox(
            list_frame,
            yscrollcommand=scrollbar.set,
            font=('Arial', 9),
            selectmode=tk.EXTENDED
        )
        self.subtopics_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.subtopics_listbox.yview)
        
        # Double-click to edit
        self.subtopics_listbox.bind('<Double-Button-1>', self.edit_subtopic)
        
        # File operations section
        file_frame = ttk.LabelFrame(self.parent, text="File Operations", padding=5)
        file_frame.pack(fill=tk.X)
        
        file_buttons_frame = ttk.Frame(file_frame)
        file_buttons_frame.pack(fill=tk.X)
        
        ttk.Button(
            file_buttons_frame, 
            text="Load from File", 
            command=self.load_from_file
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            file_buttons_frame, 
            text="Save to File", 
            command=self.save_to_file
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            file_buttons_frame, 
            text="Import CSV", 
            command=self.import_csv
        ).pack(side=tk.LEFT)
        
        # Initialize with empty data
        self.subtopics_data = []
    
    def add_subtopic(self):
        """Add a new sub-topic"""
        dialog = SubtopicDialog(self.parent, "Add Sub-topic")
        if dialog.result:
            subtopic = dialog.result
            self.subtopics_data.append(subtopic)
            self.refresh_subtopics_list()
    
    def edit_subtopic(self, event=None):
        """Edit selected sub-topic"""
        selection = self.subtopics_listbox.curselection()
        if not selection:
            return
        
        index = selection[0]
        if index < len(self.subtopics_data):
            current_data = self.subtopics_data[index]
            dialog = SubtopicDialog(self.parent, "Edit Sub-topic", current_data)
            if dialog.result:
                self.subtopics_data[index] = dialog.result
                self.refresh_subtopics_list()
    
    def remove_subtopic(self):
        """Remove selected sub-topics"""
        selection = self.subtopics_listbox.curselection()
        if not selection:
            show_error("No Selection", "Please select sub-topics to remove.")
            return
        
        # Remove in reverse order to maintain indices
        for index in reversed(selection):
            if index < len(self.subtopics_data):
                del self.subtopics_data[index]
        
        self.refresh_subtopics_list()
    
    def clear_subtopics(self):
        """Clear all sub-topics"""
        if self.subtopics_data and messagebox.askyesno("Clear Sub-topics", "Remove all sub-topics?"):
            self.subtopics_data.clear()
            self.refresh_subtopics_list()
    
    def refresh_subtopics_list(self):
        """Refresh the sub-topics listbox"""
        self.subtopics_listbox.delete(0, tk.END)
        for subtopic in self.subtopics_data:
            display_text = subtopic.get('title', 'Untitled')
            if subtopic.get('description'):
                display_text += f" - {subtopic['description'][:50]}..."
            self.subtopics_listbox.insert(tk.END, display_text)
    
    def get_topic_data(self) -> Optional[Dict[str, Any]]:
        """Get current topic data"""
        title = self.topic_title_var.get().strip()
        if not title:
            return None
        
        description = self.topic_desc_text.get(1.0, tk.END).strip()
        
        topic_data = {
            'title': title,
            'description': description,
            'sub_topics': [st.get('title', '') for st in self.subtopics_data if st.get('title')],
            'sub_topics_detailed': self.subtopics_data.copy()
        }
        
        return topic_data
    
    def set_topic_data(self, topic_data: Dict[str, Any]):
        """Set topic data"""
        self.topic_title_var.set(topic_data.get('title', ''))
        
        description = topic_data.get('description', '')
        self.topic_desc_text.delete(1.0, tk.END)
        self.topic_desc_text.insert(1.0, description)
        
        # Load sub-topics
        self.subtopics_data.clear()
        
        # Try detailed sub-topics first
        if 'sub_topics_detailed' in topic_data:
            self.subtopics_data.extend(topic_data['sub_topics_detailed'])
        elif 'sub_topics' in topic_data:
            # Convert simple list to detailed format
            for subtopic in topic_data['sub_topics']:
                if isinstance(subtopic, str):
                    self.subtopics_data.append({'title': subtopic, 'description': ''})
                elif isinstance(subtopic, dict):
                    self.subtopics_data.append(subtopic)
        
        self.refresh_subtopics_list()
    
    def clear(self):
        """Clear all input"""
        self.topic_title_var.set('')
        self.topic_desc_text.delete(1.0, tk.END)
        self.subtopics_data.clear()
        self.refresh_subtopics_list()
    
    def load_from_file(self):
        """Load topic data from file"""
        filename = filedialog.askopenfilename(
            title="Load Topic Data",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if validate_topic_structure(data):
                    self.set_topic_data(data)
                    show_info("Load Successful", f"Topic data loaded from {filename}")
                else:
                    show_error("Invalid Format", "The file does not contain valid topic data.")
                    
            except Exception as e:
                show_error("Load Error", f"Failed to load file: {str(e)}")
    
    def save_to_file(self):
        """Save topic data to file"""
        topic_data = self.get_topic_data()
        if not topic_data:
            show_error("No Data", "Please enter topic data before saving.")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Save Topic Data",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(topic_data, f, indent=2, ensure_ascii=False)
                show_info("Save Successful", f"Topic data saved to {filename}")
            except Exception as e:
                show_error("Save Error", f"Failed to save file: {str(e)}")
    
    def import_csv(self):
        """Import sub-topics from CSV file"""
        filename = filedialog.askopenfilename(
            title="Import Sub-topics from CSV",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                import csv
                with open(filename, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    imported_count = 0
                    
                    for row in reader:
                        if row and row[0].strip():  # Skip empty rows
                            title = row[0].strip()
                            description = row[1].strip() if len(row) > 1 else ''
                            
                            self.subtopics_data.append({
                                'title': title,
                                'description': description
                            })
                            imported_count += 1
                
                self.refresh_subtopics_list()
                show_info("Import Successful", f"Imported {imported_count} sub-topics from CSV.")
                
            except Exception as e:
                show_error("Import Error", f"Failed to import CSV: {str(e)}")
    
    def cleanup(self):
        """Cleanup resources"""
        pass


class SubtopicDialog:
    """Dialog for adding/editing sub-topics"""
    
    def __init__(self, parent: tk.Widget, title: str, initial_data: Optional[Dict[str, Any]] = None):
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (200 // 2)
        self.dialog.geometry(f"400x200+{x}+{y}")
        
        self.setup_ui(initial_data)
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def setup_ui(self, initial_data: Optional[Dict[str, Any]]):
        """Set up dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title field
        ttk.Label(main_frame, text="Title:").pack(anchor=tk.W)
        self.title_var = tk.StringVar()
        title_entry = ttk.Entry(main_frame, textvariable=self.title_var)
        title_entry.pack(fill=tk.X, pady=(2, 10))
        title_entry.focus()
        
        # Description field
        ttk.Label(main_frame, text="Description:").pack(anchor=tk.W)
        self.desc_text = tk.Text(main_frame, height=4, wrap=tk.WORD)
        self.desc_text.pack(fill=tk.BOTH, expand=True, pady=(2, 10))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.RIGHT)
        
        # Load initial data
        if initial_data:
            self.title_var.set(initial_data.get('title', ''))
            self.desc_text.insert(1.0, initial_data.get('description', ''))
        
        # Bind Enter key to OK
        self.dialog.bind('<Return>', lambda e: self.ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())
    
    def ok_clicked(self):
        """Handle OK button click"""
        title = self.title_var.get().strip()
        if not title:
            messagebox.showerror("Invalid Input", "Please enter a title.", parent=self.dialog)
            return
        
        description = self.desc_text.get(1.0, tk.END).strip()
        
        self.result = {
            'title': title,
            'description': description
        }
        
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """Handle Cancel button click"""
        self.dialog.destroy()
