#!/usr/bin/env python3
"""
AI Analysis Program - Main Entry Point
A comprehensive tool for topical AI analysis using Ollama API
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import Config
from utils.logging_setup import setup_logging
from gui.main_window import MainWindow

class AIAnalysisApp:
    """Main application class for AI Analysis Program"""
    
    def __init__(self):
        self.config = Config()
        self.logger = setup_logging()
        self.root = None
        self.main_window = None
        
    def initialize(self):
        """Initialize the application"""
        try:
            # Create main window
            self.root = tk.Tk()
            self.root.title("AI Analysis Program")
            self.root.geometry("1200x800")
            self.root.minsize(800, 600)
            
            # Set application icon (if available)
            try:
                self.root.iconbitmap("assets/icon.ico")
            except:
                pass  # Icon file not found, continue without it
            
            # Create main window instance
            self.main_window = MainWindow(self.root, self.config)
            
            # Configure window closing
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            self.logger.info("Application initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize application: {e}")
            messagebox.showerror("Initialization Error", 
                               f"Failed to start application:\n{str(e)}")
            return False
    
    def run(self):
        """Run the main application loop"""
        if not self.initialize():
            return
            
        try:
            self.logger.info("Starting application main loop")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            messagebox.showerror("Application Error", 
                               f"An error occurred:\n{str(e)}")
        finally:
            self.cleanup()
    
    def on_closing(self):
        """Handle application closing"""
        try:
            # Save any pending work
            if self.main_window:
                if not self.main_window.confirm_close():
                    return
                    
            self.logger.info("Application closing")
            self.root.destroy()
            
        except Exception as e:
            self.logger.error(f"Error during application closing: {e}")
            self.root.destroy()
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.main_window:
                self.main_window.cleanup()
            self.logger.info("Application cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

def main():
    """Main entry point"""
    try:
        app = AIAnalysisApp()
        app.run()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
